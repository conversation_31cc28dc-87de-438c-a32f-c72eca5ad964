<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\CalendarController::class)]
#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class CalendarReservationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['role' => 'member']);
        $this->field = Field::factory()->create([
            'name' => 'FPMP Test Soccer Field',
            'type' => 'Soccer',
            'hourly_rate' => 75.00,
            'status' => 'Active',
            'opening_time' => '07:00',
            'closing_time' => '23:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function calendar_page_loads_successfully()
    {
        $this->actingAs($this->user)
            ->get(route('calendar.index'))
            ->assertStatus(200)
            ->assertSee('calendar')
            ->assertSee('Loading calendar...');
    }

    #[Test]
    public function calendar_page_includes_week_numbers_configuration()
    {
        $response = $this->actingAs($this->user)
            ->get(route('calendar.index'));

        $response->assertStatus(200)
            ->assertSee('weekNumbers: true', false)
            ->assertSee('weekNumberCalculation: \'ISO\'', false);
    }

    #[Test]
    public function calendar_redirects_to_reservation_form_with_data()
    {
        $this->actingAs($this->user)
            ->get(route('reservations.create', [
                'field_id' => $this->field->id,
                'booking_date' => now()->addDays(1)->format('Y-m-d'),
                'start_time' => '10:00',
                'duration_hours' => 2,
            ]))
            ->assertStatus(200)
            ->assertViewIs('reservations.create')
            ->assertViewHas('selectedField', $this->field)
            ->assertViewHas('selectedDate', now()->addDays(1)->format('Y-m-d'))
            ->assertViewHas('selectedTime', '10:00')
            ->assertViewHas('selectedDuration', 2);
    }

    #[Test]
    public function ajax_reservation_creation_works()
    {
        $reservationData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00', // 2 hours duration
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $reservationData);

        // Should redirect on success (status 302)
        $response->assertStatus(302);

        // Verify reservation was created
        $this->assertDatabaseCount('bookings', 1);

        $booking = \App\Models\Booking::first();
        $this->assertNotNull($booking);
        $this->assertEquals($this->user->id, $booking->user_id);
        $this->assertEquals($this->field->id, $booking->field_id);
        $this->assertEquals($reservationData['booking_date'], $booking->booking_date->format('Y-m-d'));
        $this->assertEquals($reservationData['start_time'], $booking->start_time);
        $this->assertEquals(2, $booking->duration_hours);
        $this->assertEquals(150.00, $booking->total_cost);
        $this->assertEquals('Confirmed', $booking->status);
    }

    #[Test]
    public function ajax_reservation_returns_validation_errors()
    {
        $invalidData = [
            'field_id' => 999, // Non-existent field
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '12:00', // 2 hours duration
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $invalidData);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors'])
            ->assertJsonValidationErrors(['field_id']);
    }

    #[Test]
    public function ajax_reservation_allows_past_dates()
    {
        $pastDateData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->subDays(1)->format('Y-m-d'), // Past date
            'start_time' => '10:00',
            'end_time' => '12:00',
            'customer_name' => 'Test Customer',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $pastDateData);

        $response->assertStatus(302) // Redirect on success
            ->assertRedirect();

        // Verify the reservation was created
        $this->assertDatabaseHas('bookings', [
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);
    }

    #[Test]
    public function ajax_reservation_handles_availability_conflicts()
    {
        // Create existing reservation
        $this->actingAs($this->user)
            ->post(route('reservations.store'), [
                'field_id' => $this->field->id,
                'booking_date' => now()->addDays(1)->format('Y-m-d'),
                'start_time' => '10:00',
                'end_time' => '12:00', // 2 hours duration
            ]);

        // Try to create conflicting reservation via AJAX
        $conflictingData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '11:00', // Overlaps with existing reservation
            'end_time' => '13:00', // 2 hours duration
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $conflictingData);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors'])
            ->assertJsonValidationErrors(['start_time']);
    }

    #[Test]
    public function reservation_form_pre_populates_from_url_parameters()
    {
        $response = $this->actingAs($this->user)
            ->get(route('reservations.create', [
                'field_id' => $this->field->id,
                'booking_date' => '2024-06-25',
                'start_time' => '14:00',
            ]));

        $response->assertStatus(200);

        $content = $response->getContent();

        // Check that the form contains the pre-selected values
        // Date input should have the correct value
        $response->assertSee('value="2024-06-25"', false);

        // Field option should be selected (check for both value and selected attributes)
        $this->assertMatchesRegularExpression(
            '/<option[^>]*value="'.$this->field->id.'"[^>]*selected[^>]*>/',
            $content,
            'Field option with value="'.$this->field->id.'" should be selected'
        );

        // Start time should be pre-selected in JavaScript (we can't easily test this in a unit test)
        // Just verify the form loads successfully with the parameters
        $response->assertViewHas('selectedTime', '14:00');
    }

    #[Test]
    public function reservation_handles_working_hours_validation()
    {
        $invalidData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '06:00', // Outside working hours (before opening)
            'end_time' => '06:30', // 30 minutes duration - still outside working hours
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $invalidData);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors'])
            ->assertJsonValidationErrors(['start_time']);

        $responseData = $response->json();

        // The error is returned as a string, not an array
        $this->assertStringContainsString('working hours', $responseData['errors']['start_time']);
    }

    #[Test]
    public function reservation_handles_duration_validation()
    {
        $invalidData = [
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '20:00', // 10 hours duration - exceeds max duration
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('reservations.store'), $invalidData);

        $response->assertStatus(422)
            ->assertJsonStructure(['errors'])
            ->assertJsonValidationErrors(['end_time']);

        $responseData = $response->json();
        $this->assertStringContainsString('Duration must be between', $responseData['errors']['end_time']);
    }
}
