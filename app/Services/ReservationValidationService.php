<?php

namespace App\Services;

use App\Models\Field;
use App\Models\Reservation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class ReservationValidationService
{
    protected FieldAvailabilityService $availabilityService;

    public function __construct(FieldAvailabilityService $availabilityService)
    {
        $this->availabilityService = $availabilityService;
    }

    /**
     * Validate reservation creation data
     */
    public function validateReservationData(array $data, ?int $excludeReservationId = null): array
    {
        $errors = [];

        // Basic field validation
        $validator = Validator::make($data, [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'integer', 'min:1'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
        ]);

        if ($validator->fails()) {
            return $validator->errors()->toArray();
        }

        $field = Field::findOrFail($data['field_id']);

        // Business rule validations
        $businessRuleErrors = $this->validateBusinessRules($field, $data, $excludeReservationId);

        return array_merge($errors, $businessRuleErrors);
    }

    /**
     * Validate business rules for reservations
     */
    public function validateBusinessRules(Field $field, array $data, ?int $excludeReservationId = null): array
    {
        $errors = [];

        // 1. Field status validation
        if ($field->status !== 'Active') {
            $errors['field_id'] = ['This field is currently not available for reservations.'];
        }

        // 2. Date validation - removed past date restriction to allow historical bookings
        $bookingDate = Carbon::createFromFormat('Y-m-d', $data['booking_date'], 'America/Curacao');

        // 3. Duration validation
        if (! $this->isValidDuration($field, $data['duration_hours'])) {
            $errors['duration_hours'] = [
                "Duration must be between {$field->min_booking_hours} and {$field->max_booking_hours} hours for this field.",
            ];
        }

        // 4. Working hours validation using America/Curacao timezone
        $startTime = $data['start_time'];
        $endTime = Carbon::parse($startTime, 'America/Curacao')->addHours((int) $data['duration_hours'])->format('H:i');

        if (! $this->availabilityService->isWithinWorkingHours($field, $startTime, $endTime)) {
            $errors['start_time'] = [
                "Reservation must be within field working hours ({$field->opening_time} - {$field->closing_time}).",
            ];
        }

        // 5. Availability validation
        if (! $this->availabilityService->isFieldAvailable($field, $data['booking_date'], $startTime, $endTime, $excludeReservationId)) {
            $errors['start_time'] = ['The selected time slot is not available.'];
        }

        // 6. Maximum advance booking validation (e.g., 90 days) using America/Curacao timezone
        if ($bookingDate->diffInDays(now('America/Curacao')) > 90) {
            $errors['booking_date'] = ['Reservations can only be made up to 90 days in advance.'];
        }

        // 7. Weekend/Holiday restrictions (if any)
        $weekendErrors = $this->validateWeekendRestrictions($field, $bookingDate, $startTime);
        if (! empty($weekendErrors)) {
            $errors = array_merge($errors, $weekendErrors);
        }

        return $errors;
    }

    /**
     * Validate if a reservation can be modified
     */
    public function validateReservationModification(Reservation $reservation): array
    {
        $errors = [];

        // 1. Check if reservation exists and belongs to user
        if (! $reservation) {
            $errors['reservation'] = ['Reservation not found.'];

            return $errors;
        }

        // 2. Check if reservation can be modified
        if (! $reservation->canBeModified()) {
            $errors['reservation'] = [
                'This reservation cannot be modified. Modifications must be made before the scheduled time.',
            ];
        }

        // 3. Check reservation status
        if (! in_array($reservation->status, ['Pending', 'Confirmed'])) {
            $errors['status'] = ['Only pending or confirmed reservations can be modified.'];
        }

        // 4. Check if reservation date is in the future
        if ($reservation->booking_date->isPast()) {
            $errors['booking_date'] = ['Cannot modify past reservations.'];
        }

        return $errors;
    }

    /**
     * Validate if a reservation can be cancelled
     */
    public function validateReservationCancellation(Reservation $reservation): array
    {
        $errors = [];

        // 1. Check if reservation can be cancelled
        if (! $reservation->canBeCancelled()) {
            $errors['reservation'] = [
                'This reservation cannot be cancelled. Cancellations must be made before the scheduled time.',
            ];
        }

        // 2. Check reservation status
        if (! in_array($reservation->status, ['Pending', 'Confirmed'])) {
            $errors['status'] = ['Only pending or confirmed reservations can be cancelled.'];
        }

        // 3. Check if reservation date is in the future
        if ($reservation->booking_date->isPast()) {
            $errors['booking_date'] = ['Cannot cancel past reservations.'];
        }

        return $errors;
    }

    /**
     * Validate duration for a specific field
     */
    private function isValidDuration(Field $field, int $durationHours): bool
    {
        return $durationHours >= $field->min_booking_hours &&
               $durationHours <= $field->max_booking_hours;
    }

    /**
     * Validate weekend and holiday restrictions
     */
    private function validateWeekendRestrictions(Field $field, Carbon $date, string $startTime): array
    {
        $errors = [];

        // For Phase 1, no weekend restrictions
        // This method is prepared for future enhancements

        // Example weekend restriction (commented out for Phase 1):
        /*
        if ($date->isWeekend()) {
            $hour = (int) substr($startTime, 0, 2);
            if ($hour < 10 || $hour > 18) {
                $errors['start_time'] = ['Weekend reservations are only available between 10:00 AM and 6:00 PM.'];
            }
        }
        */

        return $errors;
    }

    /**
     * Validate user reservation limits
     */
    public function validateUserReservationLimits(int $userId, Carbon $bookingDate): array
    {
        $errors = [];

        // 1. Check daily reservation limit (max 2 reservations per day)
        $dailyReservations = Reservation::forUser($userId)
            ->where('booking_date', $bookingDate->format('Y-m-d'))
            ->active()
            ->count();

        if ($dailyReservations >= 2) {
            $errors['booking_date'] = ['You can only make up to 2 reservations per day.'];
        }

        // 2. Check weekly reservation limit (max 5 reservations per week)
        $weekStart = $bookingDate->copy()->startOfWeek();
        $weekEnd = $bookingDate->copy()->endOfWeek();

        $weeklyReservations = Reservation::forUser($userId)
            ->whereBetween('booking_date', [$weekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
            ->active()
            ->count();

        if ($weeklyReservations >= 5) {
            $errors['booking_date'] = ['You can only make up to 5 reservations per week.'];
        }

        // 3. Check for overlapping reservations (user can't have overlapping bookings)
        $overlappingReservations = Reservation::forUser($userId)
            ->where('booking_date', $bookingDate->format('Y-m-d'))
            ->active()
            ->count();

        if ($overlappingReservations > 0) {
            // This will be checked more specifically in the availability service
        }

        return $errors;
    }

    /**
     * Validate field capacity vs group size
     */
    public function validateFieldCapacity(Field $field, ?int $groupSize = null): array
    {
        $errors = [];

        if ($groupSize && $groupSize > $field->capacity) {
            $errors['group_size'] = [
                "Group size ({$groupSize}) exceeds field capacity ({$field->capacity} people).",
            ];
        }

        return $errors;
    }

    /**
     * Comprehensive validation for reservation creation
     */
    public function validateReservationCreation(array $data, int $userId): array
    {
        // 1. Basic data validation
        $errors = $this->validateReservationData($data);

        if (! empty($errors)) {
            return $errors;
        }

        // 2. User-specific validations using America/Curacao timezone
        $bookingDate = Carbon::createFromFormat('Y-m-d', $data['booking_date'], 'America/Curacao');
        $userLimitErrors = $this->validateUserReservationLimits($userId, $bookingDate);
        $errors = array_merge($errors, $userLimitErrors);

        // 3. Field capacity validation (if group size is provided)
        if (isset($data['group_size'])) {
            $field = Field::findOrFail($data['field_id']);
            $capacityErrors = $this->validateFieldCapacity($field, $data['group_size']);
            $errors = array_merge($errors, $capacityErrors);
        }

        return $errors;
    }

    /**
     * Comprehensive validation for reservation update
     */
    public function validateReservationUpdate(Reservation $reservation, array $data): array
    {
        // 1. Check if reservation can be modified
        $modificationErrors = $this->validateReservationModification($reservation);

        if (! empty($modificationErrors)) {
            return $modificationErrors;
        }

        // 2. Validate new data
        $dataErrors = $this->validateReservationData($data, $reservation->id);

        return $dataErrors;
    }

    /**
     * Get validation rules for forms
     */
    public function getValidationRules(): array
    {
        return [
            'field_id' => ['required', 'exists:fields,id'],
            'booking_date' => ['required', 'date'],
            'start_time' => ['required', 'regex:/^([0-9]|[01][0-9]|2[0-3]):[0-5][0-9](?::[0-5][0-9])?$/'],
            'duration_hours' => ['required', 'integer', 'min:1', 'max:8'],
            'customer_name' => ['nullable', 'string', 'max:255'],
            'customer_email' => ['nullable', 'email', 'max:255'],
            'customer_phone' => ['nullable', 'string', 'max:20'],
            'special_requests' => ['nullable', 'string', 'max:1000'],
        ];
    }

    /**
     * Get validation messages
     */
    public function getValidationMessages(): array
    {
        return [
            'field_id.required' => 'Please select a field.',
            'field_id.exists' => 'The selected field is invalid.',
            'booking_date.required' => 'Please select a booking date.',
            'booking_date.date' => 'Please enter a valid date.',

            'start_time.required' => 'Please select a start time.',
            'start_time.date_format' => 'Please enter a valid time format (HH:MM).',
            'duration_hours.required' => 'Please select a duration.',
            'duration_hours.integer' => 'Duration must be a whole number.',
            'duration_hours.min' => 'Minimum duration is 1 hour.',
            'duration_hours.max' => 'Maximum duration is 8 hours.',
            'customer_email.email' => 'Please enter a valid email address.',
            'special_requests.max' => 'Special requests cannot exceed 1000 characters.',
        ];
    }
}
